'use client'

import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase/client'
import { Profile, UserRole } from '@/types/database'

interface AuthState {
  user: User | null
  profile: Profile | null
  loading: boolean
  isAuthenticated: boolean
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
    isAuthenticated: false,
  })

  const supabase = createClient()

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      console.log('Getting initial session...')
      console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
      console.log('Supabase Anon Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...')

      const { data: { session }, error } = await supabase.auth.getSession()
      console.log('Session result:', { session, error })
      console.log('Session user:', session?.user)
      console.log('Session access token:', session?.access_token ? 'Present' : 'Missing')

      if (session?.user) {
        console.log('User found in session:', session.user.id)
        const profile = await fetchProfile(session.user.id)
        setAuthState({
          user: session.user,
          profile,
          loading: false,
          isAuthenticated: true,
        })
      } else {
        console.log('No user found in session')
        setAuthState({
          user: null,
          profile: null,
          loading: false,
          isAuthenticated: false,
        })
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          const profile = await fetchProfile(session.user.id)
          setAuthState({
            user: session.user,
            profile,
            loading: false,
            isAuthenticated: true,
          })
        } else {
          setAuthState({
            user: null,
            profile: null,
            loading: false,
            isAuthenticated: false,
          })
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchProfile = async (userId: string): Promise<Profile | null> => {
    try {
      console.log('Fetching profile for user:', userId)
      console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') {
          console.log('Profile not found, creating basic profile...')
          const { data: user } = await supabase.auth.getUser()
          if (user.user) {
            const newProfile = {
              id: userId,
              email: user.user.email,
              full_name: user.user.user_metadata?.full_name || 'User',
              role: 'admin' as const, // Make first user admin, others viewer
            }

            const { data: createdProfile, error: createError } = await supabase
              .from('profiles')
              .insert(newProfile)
              .select()
              .single()

            if (createError) {
              console.error('Error creating profile:', createError)
              // Return a default profile if database creation fails
              return {
                id: userId,
                email: user.user.email || '',
                full_name: user.user.user_metadata?.full_name || 'User',
                role: 'admin',
                avatar_url: null,
                bio: null,
                website: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }
            }

            console.log('Profile created successfully:', createdProfile)
            return createdProfile
          }
        }
        // Return a default profile if there's any other error
        const { data: user } = await supabase.auth.getUser()
        if (user.user) {
          return {
            id: userId,
            email: user.user.email || '',
            full_name: user.user.user_metadata?.full_name || 'User',
            role: 'admin',
            avatar_url: null,
            bio: null,
            website: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
        return null
      }
      console.log('Profile fetched successfully:', data)
      return data
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })
    return { data, error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!authState.user) return { error: new Error('Not authenticated') }

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', authState.user.id)
      .select()
      .single()

    if (!error && data) {
      setAuthState(prev => ({
        ...prev,
        profile: data,
      }))
    }

    return { data, error }
  }

  const hasRole = (role: UserRole): boolean => {
    if (!authState.profile) return false
    
    const roleHierarchy: Record<UserRole, number> = {
      viewer: 1,
      contributor: 2,
      editor: 3,
      admin: 4,
    }

    return roleHierarchy[authState.profile.role] >= roleHierarchy[role]
  }

  const canEdit = (authorId?: string): boolean => {
    if (!authState.profile) return false
    if (authState.profile.role === 'admin' || authState.profile.role === 'editor') return true
    return authState.profile.id === authorId
  }

  // Additional helper functions for role checking
  const isAdmin = () => {
    const result = authState.profile?.role === 'admin'
    console.log('isAdmin check - profile:', authState.profile)
    console.log('isAdmin check - role:', authState.profile?.role)
    console.log('isAdmin result:', result)
    return result
  }
  const isEditor = () => authState.profile?.role === 'editor' || isAdmin()
  const isContributor = () => authState.profile?.role === 'contributor' || isEditor()

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    updateProfile,
    hasRole,
    canEdit,
    isAdmin,
    isEditor,
    isContributor,
    refreshProfile: () => authState.user && fetchProfile(authState.user.id),
  }
}
