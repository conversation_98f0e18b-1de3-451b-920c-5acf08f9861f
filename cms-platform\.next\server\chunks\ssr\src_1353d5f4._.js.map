{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/components/ui/Input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/components/ui/Label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/components/ui/Textarea.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AASA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/components/ui/Select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,qMAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/app/admin/blog/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Input } from '@/components/ui/Input'\nimport { Label } from '@/components/ui/Label'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/Select'\nimport { ArrowLeft, Save, Eye } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport { useForm } from 'react-hook-form'\n\ninterface BlogPostForm {\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  status: 'draft' | 'published' | 'archived'\n  featured_image_url?: string\n  meta_title?: string\n  meta_description?: string\n}\n\nexport default function NewBlogPost() {\n  const { user, isAdmin, loading } = useAuth()\n  const router = useRouter()\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  \n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: { errors }\n  } = useForm<BlogPostForm>({\n    defaultValues: {\n      status: 'draft'\n    }\n  })\n\n  const watchTitle = watch('title')\n\n  // Auto-generate slug from title\n  const generateSlug = (title: string) => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9 -]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/-+/g, '-')\n      .trim()\n  }\n\n  const onTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const title = e.target.value\n    setValue('title', title)\n    setValue('slug', generateSlug(title))\n  }\n\n  const onSubmit = async (data: BlogPostForm) => {\n    // TEMPORARY: Bypass user check for testing\n    const bypassAuth = true\n    if (!bypassAuth && (!user || !isAdmin())) return\n\n    setIsSubmitting(true)\n    try {\n      const supabase = createClient()\n      \n      const postData = {\n        ...data,\n        author_id: user?.id || '31a74a55-e7ce-4792-9738-4797533f0a75', // Use your admin user ID as fallback\n        published_at: data.status === 'published' ? new Date().toISOString() : null\n      }\n\n      const { data: newPost, error } = await supabase\n        .from('blog_posts')\n        .insert([postData])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      router.push('/admin/blog')\n    } catch (error) {\n      console.error('Error creating post:', error)\n      alert('Failed to create post')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  // Debug logging\n  console.log('Loading:', loading)\n  console.log('User:', user)\n  console.log('isAdmin():', isAdmin())\n  console.log('User role:', user?.role)\n\n  // TEMPORARY: Bypass authentication for testing\n  // TODO: Remove this and fix authentication properly\n  const bypassAuth = true\n\n  // Show loading state while authentication is being checked\n  if (loading && !bypassAuth) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600 dark:text-gray-400\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!bypassAuth && (!user || !isAdmin())) {\n    console.log('Redirecting to unauthorized - User:', !!user, 'isAdmin:', isAdmin())\n    router.push('/unauthorized')\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center gap-4 mb-4\">\n              <Link href=\"/admin/blog\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back to Posts\n                </Button>\n              </Link>\n            </div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Create New Blog Post\n            </h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n              Write and publish a new blog post\n            </p>\n          </div>\n\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Basic Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Basic Information</CardTitle>\n                <CardDescription>\n                  Enter the basic details for your blog post\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"title\">Title *</Label>\n                  <Input\n                    id=\"title\"\n                    {...register('title', { required: 'Title is required' })}\n                    onChange={onTitleChange}\n                    placeholder=\"Enter post title\"\n                  />\n                  {errors.title && (\n                    <p className=\"text-sm text-red-600 mt-1\">{errors.title.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <Label htmlFor=\"slug\">URL Slug *</Label>\n                  <Input\n                    id=\"slug\"\n                    {...register('slug', { required: 'Slug is required' })}\n                    placeholder=\"url-friendly-slug\"\n                  />\n                  {errors.slug && (\n                    <p className=\"text-sm text-red-600 mt-1\">{errors.slug.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <Label htmlFor=\"excerpt\">Excerpt</Label>\n                  <Textarea\n                    id=\"excerpt\"\n                    {...register('excerpt')}\n                    placeholder=\"Brief description of the post\"\n                    rows={3}\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"status\">Status</Label>\n                  <Select\n                    onValueChange={(value) => setValue('status', value as any)}\n                    defaultValue=\"draft\"\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select status\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"draft\">Draft</SelectItem>\n                      <SelectItem value=\"published\">Published</SelectItem>\n                      <SelectItem value=\"archived\">Archived</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Content */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Content</CardTitle>\n                <CardDescription>\n                  Write the main content of your blog post\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div>\n                  <Label htmlFor=\"content\">Content *</Label>\n                  <Textarea\n                    id=\"content\"\n                    {...register('content', { required: 'Content is required' })}\n                    placeholder=\"Write your blog post content here...\"\n                    rows={15}\n                    className=\"font-mono\"\n                  />\n                  {errors.content && (\n                    <p className=\"text-sm text-red-600 mt-1\">{errors.content.message}</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* SEO Settings */}\n            <Card>\n              <CardHeader>\n                <CardTitle>SEO Settings</CardTitle>\n                <CardDescription>\n                  Optimize your post for search engines\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"meta_title\">Meta Title</Label>\n                  <Input\n                    id=\"meta_title\"\n                    {...register('meta_title')}\n                    placeholder=\"SEO title (leave empty to use post title)\"\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"meta_description\">Meta Description</Label>\n                  <Textarea\n                    id=\"meta_description\"\n                    {...register('meta_description')}\n                    placeholder=\"SEO description (leave empty to use excerpt)\"\n                    rows={3}\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"featured_image_url\">Featured Image URL</Label>\n                  <Input\n                    id=\"featured_image_url\"\n                    {...register('featured_image_url')}\n                    placeholder=\"https://example.com/image.jpg\"\n                    type=\"url\"\n                  />\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Actions */}\n            <div className=\"flex justify-end gap-4\">\n              <Link href=\"/admin/blog\">\n                <Button variant=\"outline\" type=\"button\">\n                  Cancel\n                </Button>\n              </Link>\n              <Button type=\"submit\" disabled={isSubmitting}>\n                <Save className=\"w-4 h-4 mr-2\" />\n                {isSubmitting ? 'Creating...' : 'Create Post'}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,eAAe;YACb,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,MAAM;IAEzB,gCAAgC;IAChC,MAAM,eAAe,CAAC;QACpB,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS,SAAS;QAClB,SAAS,QAAQ,aAAa;IAChC;IAEA,MAAM,WAAW,OAAO;QACtB,2CAA2C;QAC3C,MAAM,aAAa;QACnB,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,SAAS;;QAEvC,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;YAE5B,MAAM,WAAW;gBACf,GAAG,IAAI;gBACP,WAAW,MAAM,MAAM;gBACvB,cAAc,KAAK,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK;YACzE;YAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,cACL,MAAM,CAAC;gBAAC;aAAS,EACjB,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gBAAgB;IAChB,QAAQ,GAAG,CAAC,YAAY;IACxB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,cAAc,MAAM;IAEhC,+CAA+C;IAC/C,oDAAoD;IACpD,MAAM,aAAa;IAEnB,2DAA2D;IAC3D;;IAWA,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,SAAS;;IAMvC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAK5C,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAKvD,8OAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAEhD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,SAAS;4DAAE,UAAU;wDAAoB,EAAE;wDACxD,UAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,KAAK,kBACX,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAIlE,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,QAAQ;4DAAE,UAAU;wDAAmB,EAAE;wDACtD,aAAY;;;;;;oDAEb,OAAO,IAAI,kBACV,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;0DAIjE,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACF,GAAG,SAAS,UAAU;wDACvB,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,8OAAC,kIAAA,CAAA,SAAM;wDACL,eAAe,CAAC,QAAU,SAAS,UAAU;wDAC7C,cAAa;;0EAEb,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;kFAC1B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQvC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACF,GAAG,SAAS,WAAW;wDAAE,UAAU;oDAAsB,EAAE;oDAC5D,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;gDAEX,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAOxE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,aAAa;wDAC1B,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAmB;;;;;;kEAClC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACF,GAAG,SAAS,mBAAmB;wDAChC,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAqB;;;;;;kEACpC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACF,GAAG,SAAS,qBAAqB;wDAClC,aAAY;wDACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAOb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAS;;;;;;;;;;;kDAI1C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;;0DAC9B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}]}