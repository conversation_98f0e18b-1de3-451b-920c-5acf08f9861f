{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/app/api/test-supabase/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/client'\n\nexport async function GET() {\n  try {\n    console.log('Testing Supabase connection...')\n    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)\n    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...')\n    \n    const supabase = createClient()\n    \n    // Test basic connection\n    const { data, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .limit(1)\n    \n    if (error) {\n      console.error('Supabase connection error:', error)\n      return NextResponse.json({ \n        success: false, \n        error: error.message,\n        url: process.env.NEXT_PUBLIC_SUPABASE_URL,\n        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n      })\n    }\n    \n    return NextResponse.json({ \n      success: true, \n      data,\n      url: process.env.NEXT_PUBLIC_SUPABASE_URL,\n      hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n    })\n  } catch (error) {\n    console.error('Test error:', error)\n    return NextResponse.json({ \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error',\n      url: process.env.NEXT_PUBLIC_SUPABASE_URL,\n      hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,kCAAkC,sPAA2C,UAAU,GAAG,MAAM;QAE5G,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAE5B,wBAAwB;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO,MAAM,OAAO;gBACpB,GAAG;gBACH,QAAQ,CAAC;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,GAAG;YACH,QAAQ,CAAC;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,GAAG;YACH,QAAQ,CAAC;QACX;IACF;AACF", "debugId": null}}]}