-- Fix the infinite recursion issue in R<PERSON> policies

-- First, temporarily disable <PERSON><PERSON> on profiles to avoid recursion
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on profiles
DROP POLICY IF EXISTS "Users can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

-- Drop the problematic functions
DROP FUNCTION IF EXISTS public.get_user_role(UUID);
DROP FUNCTION IF EXISTS public.is_admin_or_editor(UUID);

-- Create simpler policies that don't cause recursion
-- For profiles table, we'll use simpler policies without recursive function calls

-- Allow users to view all profiles (needed for the app to function)
CREATE POLICY "Users can view all profiles" ON profiles FOR SELECT USING (true);

-- Allow users to insert their own profile
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Re-enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create a safer helper function that doesn't cause recursion
CREATE OR REPLACE FUNCTION public.get_user_role_safe(user_id UUID)
RETURNS user_role AS $$
DECLARE
  user_role_result user_role;
BEGIN
  -- Use a direct query without RLS to avoid recursion
  SELECT role INTO user_role_result FROM profiles WHERE id = user_id;
  RETURN COALESCE(user_role_result, 'viewer'::user_role);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a safer admin/editor check function
CREATE OR REPLACE FUNCTION public.is_admin_or_editor_safe(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role_result user_role;
BEGIN
  -- Use a direct query without RLS to avoid recursion
  SELECT role INTO user_role_result FROM profiles WHERE id = user_id;
  RETURN COALESCE(user_role_result, 'viewer'::user_role) IN ('admin', 'editor');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update other table policies to use the safe functions
-- Categories policies
DROP POLICY IF EXISTS "Editors and admins can manage categories" ON categories;
CREATE POLICY "Editors and admins can manage categories" ON categories FOR ALL USING (is_admin_or_editor_safe(auth.uid()));

-- Tags policies  
DROP POLICY IF EXISTS "Editors and admins can manage tags" ON tags;
CREATE POLICY "Editors and admins can manage tags" ON tags FOR ALL USING (is_admin_or_editor_safe(auth.uid()));

-- Media policies
DROP POLICY IF EXISTS "Contributors and above can upload media" ON media;
CREATE POLICY "Contributors and above can upload media" ON media FOR INSERT WITH CHECK (
  get_user_role_safe(auth.uid()) IN ('admin', 'editor', 'contributor')
);

DROP POLICY IF EXISTS "Admins and editors can update any media" ON media;
CREATE POLICY "Admins and editors can update any media" ON media FOR UPDATE USING (is_admin_or_editor_safe(auth.uid()));

DROP POLICY IF EXISTS "Admins can delete any media" ON media;
CREATE POLICY "Admins can delete any media" ON media FOR DELETE USING (get_user_role_safe(auth.uid()) = 'admin');
