{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_3360c40e._.js", "server/edge/chunks/[root-of-the-server]__e215687c._.js", "server/edge/chunks/edge-wrapper_a7b776d4.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7JSfR35fAeErPkbWbap7srlFBPnFIx2rXPqCgujMuio=", "__NEXT_PREVIEW_MODE_ID": "70cc9278460b27acce38b4f3fcfb7d37", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0454f0b27596accbd183e5db201cffbee04299f8fc4fb003d95f7437014edd08", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3b2941468772767dbd55a0377201f9bd822e5875af89f4995c05235f425b1a13"}}}, "instrumentation": null, "functions": {}}