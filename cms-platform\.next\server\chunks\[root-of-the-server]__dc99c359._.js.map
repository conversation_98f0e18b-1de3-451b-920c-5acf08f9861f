{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder/cms-platform/src/app/api/test-auth/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function GET() {\n  try {\n    console.log('Testing authentication...')\n    \n    const supabase = createClient()\n    \n    // Get the current session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n    \n    console.log('Session:', session)\n    console.log('Session error:', sessionError)\n    \n    // Get the current user\n    const { data: { user }, error: userError } = await supabase.auth.getUser()\n    \n    console.log('User:', user)\n    console.log('User error:', userError)\n    \n    return NextResponse.json({\n      success: true,\n      session: session ? {\n        user_id: session.user.id,\n        email: session.user.email,\n        expires_at: session.expires_at\n      } : null,\n      user: user ? {\n        id: user.id,\n        email: user.email\n      } : null,\n      sessionError,\n      userError\n    })\n  } catch (error) {\n    console.error('Auth test error:', error)\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAE5B,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAEjF,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,QAAQ,GAAG,CAAC,SAAS;QACrB,QAAQ,GAAG,CAAC,eAAe;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,UAAU;gBACjB,SAAS,QAAQ,IAAI,CAAC,EAAE;gBACxB,OAAO,QAAQ,IAAI,CAAC,KAAK;gBACzB,YAAY,QAAQ,UAAU;YAChC,IAAI;YACJ,MAAM,OAAO;gBACX,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;YACnB,IAAI;YACJ;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF", "debugId": null}}]}